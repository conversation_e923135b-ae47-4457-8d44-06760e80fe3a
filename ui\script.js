let currentCar = null;
let carIcons = {
    'sultan': 'fas fa-car',
    'elegy2': 'fas fa-car-side',
    'adder': 'fas fa-fighter-jet',
    'sandking': 'fas fa-truck',
    'bison': 'fas fa-bus'
};

// Default car icon
const defaultCarIcon = 'fas fa-car';

// Theme management
let currentTheme = 'light';

function toggleTheme() {
    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    const menus = document.querySelectorAll('.menu');
    const themeToggles = document.querySelectorAll('.theme-toggle');

    menus.forEach(menu => {
        menu.setAttribute('data-theme', currentTheme);
    });

    themeToggles.forEach(toggle => {
        const icon = toggle.querySelector('i');
        if (currentTheme === 'light') {
            icon.className = 'fas fa-moon';
        } else {
            icon.className = 'fas fa-sun';
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    const closeBtn = document.getElementById('closeBtn');
    const backBtn = document.getElementById('backBtn');
    const rentBtn = document.getElementById('rentBtn');
    const rentalTime = document.getElementById('rentalTime');
    const totalPrice = document.getElementById('totalPrice');
    const themeToggle = document.getElementById('themeToggle');
    const themeToggle2 = document.getElementById('themeToggle2');

    closeBtn.addEventListener('click', closeMenu);
    backBtn.addEventListener('click', showCarMenu);
    rentBtn.addEventListener('click', rentCar);
    rentalTime.addEventListener('input', updateTotalPrice);
    themeToggle.addEventListener('click', toggleTheme);
    themeToggle2.addEventListener('click', toggleTheme);
    
    window.addEventListener('message', function(event) {
        const data = event.data;

        try {
            if (data.action === 'toggleMenu') {
                if (data.show) {
                    document.body.style.display = 'flex';
                    // Add entrance animation
                    setTimeout(() => {
                        const menu = document.querySelector('.menu[style*="block"], .menu:not([style*="none"])');
                        if (menu) {
                            menu.style.animation = 'slideInRight 0.3s cubic-bezier(0.16, 1, 0.3, 1)';
                        }
                    }, 10);
                } else {
                    document.body.style.display = 'none';
                }
            }
            else if (data.action === 'showCarMenu') {
                showCarMenu(data.cars);
            }
            else if (data.action === 'showTimeMenu') {
                showTimeMenu(data.car);
            }
        } catch (error) {
            console.error('Error handling message:', error, data);
        }
    });
});

function closeMenu() {
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).catch(error => {
        console.error('Error closing menu:', error);
    });
}

function showCarMenu(cars) {
    console.log('showCarMenu called with:', cars);

    const carMenu = document.getElementById('carMenu');
    const timeMenu = document.getElementById('timeMenu');

    carMenu.style.display = 'block';
    timeMenu.style.display = 'none';

    // Sync theme
    carMenu.setAttribute('data-theme', currentTheme);
    timeMenu.setAttribute('data-theme', currentTheme);

    // If cars data is provided, use it directly
    if (cars && Array.isArray(cars)) {
        const carList = document.getElementById('carList');
        carList.innerHTML = '';

        if (cars.length === 0) {
            carList.innerHTML = '<div style="text-align: center; padding: 20px; color: #a1a1aa;">Araç bulunamadı</div>';
            return;
        }

        cars.forEach((car, index) => {
            const carItem = document.createElement('div');
            carItem.className = 'car-item';
            carItem.style.opacity = '0';
            carItem.style.transform = 'translateY(20px)';
            carItem.innerHTML = `
                <div class="car-icon">
                    <i class="${carIcons[car.model] || defaultCarIcon}"></i>
                </div>
                <div class="car-info">
                    <h3>${car.label}</h3>
                    <p>${car.category} | $${car.price}/dk</p>
                </div>
                <div class="price-tag">$${car.price}/dk</div>
            `;

            carItem.addEventListener('click', () => {
                currentCar = car;
                showTimeMenu(car);
            });

            carList.appendChild(carItem);

            // Animate in with delay
            setTimeout(() => {
                carItem.style.transition = 'all 0.4s cubic-bezier(0.16, 1, 0.3, 1)';
                carItem.style.opacity = '1';
                carItem.style.transform = 'translateY(0)';
            }, index * 100 + 100);
        });
    } else {
        console.log('No cars data provided, requesting from client');
        // Fallback: request cars from client
        fetch(`https://${GetParentResourceName()}/showCarMenu`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        }).catch(error => {
            console.error('Error requesting cars:', error);
        });
    }
}

function showTimeMenu(car) {
    console.log('showTimeMenu called with:', car);

    if (!car) {
        console.error('No car data provided to showTimeMenu');
        return;
    }

    const carMenu = document.getElementById('carMenu');
    const timeMenu = document.getElementById('timeMenu');

    carMenu.style.display = 'none';
    timeMenu.style.display = 'block';

    // Sync theme
    timeMenu.setAttribute('data-theme', currentTheme);

    // Update car info
    document.getElementById('carName').textContent = car.label || 'Unknown Car';
    document.getElementById('carCategory').textContent = `Kategori: ${car.category || 'Unknown'}`;
    document.getElementById('carPrice').textContent = `Fiyat: $${car.price || 0}/dk`;

    // Update car icon
    const carIconElement = document.querySelector('.car-icon-large i');
    if (carIconElement) {
        carIconElement.className = carIcons[car.model] || defaultCarIcon;
    }

    // Set current car for price calculation
    currentCar = car;
    updateTotalPrice();
}

function updateTotalPrice() {
    if (!currentCar) {
        console.warn('No current car selected for price calculation');
        return;
    }

    const timeInput = document.getElementById('rentalTime');
    const totalPriceElement = document.getElementById('totalPrice');

    if (!timeInput || !totalPriceElement) {
        console.error('Required elements not found for price calculation');
        return;
    }

    const time = parseInt(timeInput.value) || 0;
    const total = (currentCar.price || 0) * time;
    totalPriceElement.textContent = `$${total}`;
}

function rentCar() {
    if (!currentCar) {
        console.error('No car selected');
        return;
    }

    const time = parseInt(document.getElementById('rentalTime').value) || 0;

    if (time <= 0) {
        console.error('Invalid rental time');
        return;
    }

    fetch(`https://${GetParentResourceName()}/rentCar`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            car: currentCar,
            time: time
        })
    }).catch(error => {
        console.error('Error renting car:', error);
    });

    closeMenu();
}