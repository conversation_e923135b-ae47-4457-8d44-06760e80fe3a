let currentCar = null;
let carImages = {
    'sultan': 'assets/sultan.svg',
    'elegy2': 'assets/elegy2.svg',
    'adder': 'assets/adder.svg',
    'sandking': 'assets/sandking.svg',
    'bison': 'assets/bison.svg'
};

// Default car image as base64 (small car icon)
const defaultCarImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiByeD0iNCIgZmlsbD0iIzM3NDE1MSIvPgo8cGF0aCBkPSJNMTAgMTVIMTVMMjAgMTBINDBMNDUgMTVINTBWMjVINDVWMzBIMTVWMjVIMTBWMTVaIiBmaWxsPSIjNjM3M0ZGIi8+CjxjaXJjbGUgY3g9IjE4IiBjeT0iMjUiIHI9IjMiIGZpbGw9IiMxRjI5MzciLz4KPGNpcmNsZSBjeD0iNDIiIGN5PSIyNSIgcj0iMyIgZmlsbD0iIzFGMjkzNyIvPgo8L3N2Zz4K';

document.addEventListener('DOMContentLoaded', function() {
    const closeBtn = document.getElementById('closeBtn');
    const backBtn = document.getElementById('backBtn');
    const rentBtn = document.getElementById('rentBtn');
    const rentalTime = document.getElementById('rentalTime');
    const totalPrice = document.getElementById('totalPrice');
    
    closeBtn.addEventListener('click', closeMenu);
    backBtn.addEventListener('click', showCarMenu);
    rentBtn.addEventListener('click', rentCar);
    rentalTime.addEventListener('input', updateTotalPrice);
    
    window.addEventListener('message', function(event) {
        const data = event.data;

        try {
            if (data.action === 'toggleMenu') {
                if (data.show) {
                    document.body.style.display = 'flex';
                } else {
                    document.body.style.display = 'none';
                }
            }
            else if (data.action === 'showCarMenu') {
                showCarMenu(data.cars);
            }
            else if (data.action === 'showTimeMenu') {
                showTimeMenu(data.car);
            }
        } catch (error) {
            console.error('Error handling message:', error, data);
        }
    });
});

function closeMenu() {
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).catch(error => {
        console.error('Error closing menu:', error);
    });
}

function showCarMenu(cars) {
    console.log('showCarMenu called with:', cars);

    document.getElementById('carMenu').style.display = 'block';
    document.getElementById('timeMenu').style.display = 'none';

    // If cars data is provided, use it directly
    if (cars && Array.isArray(cars)) {
        const carList = document.getElementById('carList');
        carList.innerHTML = '';

        if (cars.length === 0) {
            carList.innerHTML = '<div style="text-align: center; padding: 20px; color: #a1a1aa;">Araç bulunamadı</div>';
            return;
        }

        cars.forEach(car => {
            const carItem = document.createElement('div');
            carItem.className = 'car-item';
            carItem.innerHTML = `
                <img src="${carImages[car.model] || 'assets/default.svg'}" alt="${car.label}" onerror="this.src='${defaultCarImage}'">
                <div class="car-info">
                    <h3>${car.label}</h3>
                    <p>${car.category} | $${car.price}/dk</p>
                </div>
                <div class="price-tag">$${car.price}/dk</div>
            `;

            carItem.addEventListener('click', () => {
                currentCar = car;
                showTimeMenu(car);
            });

            carList.appendChild(carItem);
        });
    } else {
        console.log('No cars data provided, requesting from client');
        // Fallback: request cars from client
        fetch(`https://${GetParentResourceName()}/showCarMenu`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        }).catch(error => {
            console.error('Error requesting cars:', error);
        });
    }
}

function showTimeMenu(car) {
    console.log('showTimeMenu called with:', car);

    if (!car) {
        console.error('No car data provided to showTimeMenu');
        return;
    }

    document.getElementById('carMenu').style.display = 'none';
    document.getElementById('timeMenu').style.display = 'block';

    document.getElementById('carName').textContent = car.label || 'Unknown Car';
    document.getElementById('carCategory').textContent = `Kategori: ${car.category || 'Unknown'}`;
    document.getElementById('carPrice').textContent = `Fiyat: $${car.price || 0}/dk`;

    const carImageElement = document.getElementById('carImage');
    carImageElement.src = carImages[car.model] || 'assets/default.svg';
    carImageElement.onerror = function() { this.src = defaultCarImage; };

    // Set current car for price calculation
    currentCar = car;
    updateTotalPrice();
}

function updateTotalPrice() {
    if (!currentCar) {
        console.warn('No current car selected for price calculation');
        return;
    }

    const timeInput = document.getElementById('rentalTime');
    const totalPriceElement = document.getElementById('totalPrice');

    if (!timeInput || !totalPriceElement) {
        console.error('Required elements not found for price calculation');
        return;
    }

    const time = parseInt(timeInput.value) || 0;
    const total = (currentCar.price || 0) * time;
    totalPriceElement.textContent = `$${total}`;
}

function rentCar() {
    if (!currentCar) {
        console.error('No car selected');
        return;
    }

    const time = parseInt(document.getElementById('rentalTime').value) || 0;

    if (time <= 0) {
        console.error('Invalid rental time');
        return;
    }

    fetch(`https://${GetParentResourceName()}/rentCar`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            car: currentCar,
            time: time
        })
    }).catch(error => {
        console.error('Error renting car:', error);
    });

    closeMenu();
}