let currentCar = null;
let carImages = {
    'sultan': 'assets/sultan.png',
    'elegy2': 'assets/elegy2.png',
    'adder': 'assets/adder.png',
    'sandking': 'assets/sandking.png',
    'bison': 'assets/bison.png'
};

document.addEventListener('DOMContentLoaded', function() {
    const closeBtn = document.getElementById('closeBtn');
    const backBtn = document.getElementById('backBtn');
    const rentBtn = document.getElementById('rentBtn');
    const rentalTime = document.getElementById('rentalTime');
    const totalPrice = document.getElementById('totalPrice');
    
    closeBtn.addEventListener('click', closeMenu);
    backBtn.addEventListener('click', showCarMenu);
    rentBtn.addEventListener('click', rentCar);
    rentalTime.addEventListener('input', updateTotalPrice);
    
    window.addEventListener('message', function(event) {
        const data = event.data;
        
        if (data.action === 'toggleMenu') {
            document.body.style.display = data.show ? 'flex' : 'none';
        }
        else if (data.action === 'showCarMenu') {
            showCarMenu(data.cars);
        }
        else if (data.action === 'showTimeMenu') {
            showTimeMenu(data.car);
        }
    });
});

function closeMenu() {
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    });
}

function showCarMenu() {
    document.getElementById('carMenu').style.display = 'block';
    document.getElementById('timeMenu').style.display = 'none';
    
    fetch(`https://${GetParentResourceName()}/getCars`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).then(resp => resp.json()).then(data => {
        const carList = document.getElementById('carList');
        carList.innerHTML = '';
        
        data.cars.forEach(car => {
            const carItem = document.createElement('div');
            carItem.className = 'car-item';
            carItem.innerHTML = `
                <img src="${carImages[car.model] || 'assets/default.png'}" alt="${car.label}">
                <div class="car-info">
                    <h3>${car.label}</h3>
                    <p>${car.category} | $${car.price}/dk</p>
                </div>
                <div class="price-tag">$${car.price}/dk</div>
            `;
            
            carItem.addEventListener('click', () => {
                currentCar = car;
                showTimeMenu(car);
            });
            
            carList.appendChild(carItem);
        });
    });
}

function showTimeMenu(car) {
    document.getElementById('carMenu').style.display = 'none';
    document.getElementById('timeMenu').style.display = 'block';
    
    document.getElementById('carName').textContent = car.label;
    document.getElementById('carCategory').textContent = `Kategori: ${car.category}`;
    document.getElementById('carPrice').textContent = `Fiyat: $${car.price}/dk`;
    document.getElementById('carImage').src = carImages[car.model] || 'assets/default.png';
    
    updateTotalPrice();
}

function updateTotalPrice() {
    if (!currentCar) return;
    
    const time = parseInt(document.getElementById('rentalTime').value) || 0;
    const total = currentCar.price * time;
    document.getElementById('totalPrice').textContent = `$${total}`;
}

function rentCar() {
    if (!currentCar) return;
    
    const time = parseInt(document.getElementById('rentalTime').value) || 0;
    
    fetch(`https://${GetParentResourceName()}/rentCar`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            car: currentCar,
            time: time
        })
    });
    
    closeMenu();
}