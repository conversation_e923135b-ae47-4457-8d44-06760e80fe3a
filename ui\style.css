* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    display: none;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    overflow: hidden;
}

.menu {
    background: rgba(30, 30, 46, 0.95);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    width: 90%;
    max-width: 500px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.header {
    background: linear-gradient(90deg, #2563eb, #1d4ed8);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn, .back-btn {
    background: rgba(255, 255, 255, 0.2);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.close-btn:hover, .back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.menu-items {
    max-height: 500px;
    overflow-y: auto;
    padding: 15px;
}

.car-item {
    background: rgba(46, 46, 68, 0.7);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.car-item:hover {
    background: rgba(59, 59, 89, 0.9);
    transform: translateY(-3px);
}

.car-item img {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 15px;
}

.car-info {
    flex: 1;
}

.car-info h3 {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.car-info p {
    font-size: 0.9rem;
    color: #a1a1aa;
}

.price-tag {
    background: rgba(37, 99, 235, 0.2);
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.time-container {
    padding: 20px;
}

.car-info {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    background: rgba(46, 46, 68, 0.7);
    padding: 15px;
    border-radius: 8px;
}

.car-info img {
    width: 100px;
    height: 70px;
    object-fit: cover;
    border-radius: 6px;
    margin-right: 20px;
}

.time-input {
    display: flex;
    flex-direction: column;
}

.time-input label {
    margin-bottom: 8px;
    font-size: 0.95rem;
    color: #d1d5db;
}

.time-input input {
    background: rgba(46, 46, 68, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 12px 15px;
    color: white;
    font-size: 1rem;
    margin-bottom: 20px;
    outline: none;
}

.time-input input:focus {
    border-color: #3b82f6;
}

.price-display {
    background: rgba(46, 46, 68, 0.7);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: bold;
    color: #10b981;
}

#rentBtn {
    background: linear-gradient(90deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

#rentBtn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(30, 30, 46, 0.5);
}

::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
}