@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --border-color: #e2e8f0;
    --success-color: #10b981;
    --success-hover: #059669;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Color Themes */
:root[data-color="blue"] {
    --accent-color: #3b82f6;
    --accent-hover: #2563eb;
}

:root[data-color="green"] {
    --accent-color: #10b981;
    --accent-hover: #059669;
}

:root[data-color="purple"] {
    --accent-color: #8b5cf6;
    --accent-hover: #7c3aed;
}

:root[data-color="red"] {
    --accent-color: #ef4444;
    --accent-hover: #dc2626;
}

:root[data-color="orange"] {
    --accent-color: #f97316;
    --accent-hover: #ea580c;
}

:root[data-color="pink"] {
    --accent-color: #ec4899;
    --accent-hover: #db2777;
}

:root[data-color="indigo"] {
    --accent-color: #6366f1;
    --accent-hover: #4f46e5;
}

[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-color: #334155;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

/* Dark theme color variations */
[data-theme="dark"][data-color="blue"] {
    --accent-color: #60a5fa;
    --accent-hover: #3b82f6;
}

[data-theme="dark"][data-color="green"] {
    --accent-color: #34d399;
    --accent-hover: #10b981;
}

[data-theme="dark"][data-color="purple"] {
    --accent-color: #a78bfa;
    --accent-hover: #8b5cf6;
}

[data-theme="dark"][data-color="red"] {
    --accent-color: #f87171;
    --accent-hover: #ef4444;
}

[data-theme="dark"][data-color="orange"] {
    --accent-color: #fb923c;
    --accent-hover: #f97316;
}

[data-theme="dark"][data-color="pink"] {
    --accent-color: #f472b6;
    --accent-hover: #ec4899;
}

[data-theme="dark"][data-color="indigo"] {
    --accent-color: #818cf8;
    --accent-hover: #6366f1;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    display: none;
    justify-content: flex-end;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    padding-right: 40px;
    z-index: 1000;
    pointer-events: none;
}

.menu {
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
    width: 420px;
    max-height: 80vh;
    overflow: hidden;
    border: 1px solid var(--border-color);
    animation: slideInRight 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    pointer-events: auto;
    position: relative;
}

.header {
    background: var(--bg-secondary);
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    border-bottom: 1px solid var(--border-color);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.025em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-toggle, .color-picker {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    font-size: 16px;
}

.theme-toggle:hover, .color-picker:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.color-dropdown {
    position: fixed;
    top: 60px;
    right: 40px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    padding: 8px;
    z-index: 1001;
    min-width: 150px;
}

.color-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-option:hover {
    background: var(--bg-secondary);
}

.color-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
}

.color-option span {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 500;
}

.close-btn, .back-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 16px;
    color: var(--text-secondary);
}

.close-btn:hover, .back-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.menu-items {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
    padding: 20px 24px;
    gap: 8px;
    display: flex;
    flex-direction: column;
}

.car-item {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    position: relative;
}

.car-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-color);
    background: var(--bg-tertiary);
}

.car-icon {
    width: 64px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin-right: 16px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--accent-color);
    font-size: 24px;
    transition: all 0.2s ease;
}

.car-item:hover .car-icon {
    color: var(--accent-hover);
    background: var(--accent-color);
    color: white;
    transform: scale(1.05);
}

.car-icon-large {
    width: 96px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    margin-right: 20px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--accent-color);
    font-size: 36px;
}



.car-item .car-info {
    flex: 1;
}

.car-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.car-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.price-tag {
    background: var(--accent-color);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    white-space: nowrap;
}

.time-container {
    padding: 24px;
}

.time-container .car-info {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

/* Car icon styles are handled by .car-icon-large class */

.car-item .car-info {
    flex: 1;
}

.time-input {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.time-input label {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 6px;
}

.time-input input {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 400;
    outline: none;
    transition: all 0.2s ease;
}

.time-input input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.time-input input::placeholder {
    color: var(--text-tertiary);
    font-weight: 400;
}

.price-display {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 16px;
    border-radius: 8px;
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--success-color);
}

#rentBtn {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

#rentBtn:hover {
    background: var(--success-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding-right: 20px;
        padding-left: 20px;
        justify-content: center;
    }

    .menu {
        width: 100%;
        max-width: 400px;
    }

    .header {
        padding: 16px 20px;
    }

    .header h1 {
        font-size: 1.25rem;
    }

    .menu-items {
        padding: 16px 20px;
    }

    .car-item {
        padding: 12px;
    }

    .car-icon {
        width: 56px;
        height: 36px;
        font-size: 20px;
    }

    .time-container {
        padding: 20px;
    }

    .time-container .car-info {
        flex-direction: column;
        text-align: center;
        padding: 16px;
    }

    .car-icon-large {
        width: 80px;
        height: 52px;
        margin-right: 0;
        margin-bottom: 12px;
        font-size: 28px;
    }
}