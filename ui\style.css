@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --border-color: #e2e8f0;
    --accent-color: #3b82f6;
    --accent-hover: #2563eb;
    --success-color: #10b981;
    --success-hover: #059669;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --border-color: #334155;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    display: none;
    justify-content: flex-end;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    padding-right: 40px;
    z-index: 1000;
    pointer-events: none;
}

.menu {
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
    width: 420px;
    max-height: 80vh;
    overflow: hidden;
    border: 1px solid var(--border-color);
    animation: slideInRight 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    pointer-events: auto;
    position: relative;
}

.header {
    background: var(--bg-secondary);
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    border-bottom: 1px solid var(--border-color);
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    letter-spacing: -0.025em;
    display: flex;
    align-items: center;
    gap: 8px;
}

.theme-toggle {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-secondary);
    font-size: 16px;
    margin-right: 12px;
}

.theme-toggle:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.close-btn, .back-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 16px;
    color: var(--text-secondary);
}

.close-btn:hover, .back-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.menu-items {
    max-height: calc(80vh - 120px);
    overflow-y: auto;
    padding: 20px 24px;
    gap: 8px;
    display: flex;
    flex-direction: column;
}

.car-item {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--border-color);
    position: relative;
}

.car-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-color);
    background: var(--bg-tertiary);
}

.car-item img {
    width: 64px;
    height: 40px;
    object-fit: contain;
    border-radius: 8px;
    margin-right: 16px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}



.car-item .car-info {
    flex: 1;
}

.car-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
    letter-spacing: -0.025em;
}

.car-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.price-tag {
    background: var(--accent-color);
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    color: white;
    white-space: nowrap;
}

.time-container {
    padding: 24px;
}

.time-container .car-info {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.time-container .car-info img {
    width: 96px;
    height: 64px;
    object-fit: contain;
    border-radius: 8px;
    margin-right: 20px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}

.car-item .car-info {
    flex: 1;
}

.time-input {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.time-input label {
    font-size: 0.875rem;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 6px;
}

.time-input input {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 400;
    outline: none;
    transition: all 0.2s ease;
}

.time-input input:focus {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.time-input input::placeholder {
    color: var(--text-tertiary);
    font-weight: 400;
}

.price-display {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 16px;
    border-radius: 8px;
    text-align: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--success-color);
}

#rentBtn {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
}

#rentBtn:hover {
    background: var(--success-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Animations */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-tertiary);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding-right: 20px;
        padding-left: 20px;
        justify-content: center;
    }

    .menu {
        width: 100%;
        max-width: 400px;
    }

    .header {
        padding: 16px 20px;
    }

    .header h1 {
        font-size: 1.25rem;
    }

    .menu-items {
        padding: 16px 20px;
    }

    .car-item {
        padding: 12px;
    }

    .car-item img {
        width: 56px;
        height: 36px;
    }

    .time-container {
        padding: 20px;
    }

    .time-container .car-info {
        flex-direction: column;
        text-align: center;
        padding: 16px;
    }

    .time-container .car-info img {
        width: 80px;
        height: 52px;
        margin-right: 0;
        margin-bottom: 12px;
    }
}