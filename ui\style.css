@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

body {
    background: transparent;
    color: #ffffff;
    display: none;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    overflow: hidden;
    z-index: 1000;
}

.menu {
    background: linear-gradient(145deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 20px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 520px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    animation: slideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 24px 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.close-btn, .back-btn {
    background: rgba(255, 255, 255, 0.15);
    width: 36px;
    height: 36px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.close-btn:hover, .back-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-items {
    max-height: 520px;
    overflow-y: auto;
    padding: 24px 28px;
    gap: 12px;
    display: flex;
    flex-direction: column;
}

.car-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
    overflow: hidden;
}

.car-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(103, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 16px;
}

.car-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.15);
}

.car-item:hover::before {
    opacity: 1;
}

.car-item img {
    width: 72px;
    height: 48px;
    object-fit: contain;
    border-radius: 12px;
    margin-right: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 1;
}



.car-item .car-info {
    position: relative;
    z-index: 1;
    flex: 1;
}

.car-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 6px;
    color: #ffffff;
    letter-spacing: -0.025em;
}

.car-info p {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

.price-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 8px 16px;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    color: #ffffff;
    position: relative;
    z-index: 1;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.time-container {
    padding: 28px;
}

.time-container .car-info {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    padding: 24px;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.time-container .car-info img {
    width: 120px;
    height: 80px;
    object-fit: contain;
    border-radius: 12px;
    margin-right: 24px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.car-item .car-info {
    flex: 1;
}

.time-input {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.time-input label {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 8px;
}

.time-input input {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    padding: 16px 20px;
    color: white;
    font-size: 1.1rem;
    font-weight: 500;
    outline: none;
    transition: all 0.2s ease;
}

.time-input input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
}

.time-input input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
}

.price-display {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.15) 100%);
    border: 1px solid rgba(16, 185, 129, 0.2);
    padding: 20px;
    border-radius: 16px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #10b981;
    letter-spacing: -0.025em;
}

#rentBtn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 18px 32px;
    border-radius: 16px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    letter-spacing: 0.025em;
    text-transform: uppercase;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

#rentBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

/* Animations */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu {
        width: 95%;
        max-width: none;
        margin: 20px;
    }

    .header {
        padding: 20px 24px;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .menu-items {
        padding: 20px 24px;
    }

    .car-item {
        padding: 16px;
    }

    .car-item img {
        width: 60px;
        height: 40px;
    }

    .time-container {
        padding: 24px;
    }

    .time-container .car-info {
        flex-direction: column;
        text-align: center;
        padding: 20px;
    }

    .time-container .car-info img {
        width: 100px;
        height: 65px;
        margin-right: 0;
        margin-bottom: 16px;
    }

    #rentBtn {
        padding: 16px 28px;
        font-size: 1.1rem;
    }
}