<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- <PERSON><PERSON>esi -->
    <div id="carMenu" class="menu" data-theme="light">
        <div class="header">
            <h1><PERSON><PERSON></h1>
            <div style="display: flex; align-items: center; gap: 8px;">
                <div class="theme-toggle" id="themeToggle">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="close-btn" id="closeBtn">
                    <i class="fas fa-times"></i>
                </div>
            </div>
        </div>
        <div class="menu-items" id="carList"></div>
    </div>
    
    <!-- Süre Seçimi -->
    <div id="timeMenu" class="menu" style="display:none" data-theme="light">
        <div class="header">
            <h1 id="timeMenuTitle">Süre Seçimi</h1>
            <div style="display: flex; align-items: center; gap: 8px;">
                <div class="theme-toggle" id="themeToggle2">
                    <i class="fas fa-moon"></i>
                </div>
                <div class="back-btn" id="backBtn">
                    <i class="fas fa-arrow-left"></i>
                </div>
            </div>
        </div>
        <div class="time-container">
            <div class="car-info" id="carInfoContainer">
                <div class="car-icon-large">
                    <i class="fas fa-car"></i>
                </div>
                <div class="car-details">
                    <h2 id="carName"></h2>
                    <p id="carCategory"></p>
                    <p id="carPrice"></p>
                </div>
            </div>
            <div class="time-input">
                <label for="rentalTime">Kiralama Süresi (Dakika)</label>
                <input type="number" id="rentalTime" min="1" max="120" value="30" placeholder="Süre girin...">
                <div class="price-display">
                    <span>Toplam Fiyat: </span>
                    <span id="totalPrice">$0</span>
                </div>
                <button id="rentBtn">Kirala</button>
            </div>
        </div>
    </div>



    <script src="script.js"></script>
</body>
</html>