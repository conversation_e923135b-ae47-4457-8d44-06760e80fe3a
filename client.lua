local QBCore = exports['qb-core']:GetCoreObject()
local rentalNPC = nil
local currentRental = nil

-- <PERSON><PERSON><PERSON><PERSON> fonksiyonu
local function ShowNotification(message, type)
    QBCore.Functions.Notify(message, type or 'primary')
end

-- NPC oluşturma
local function CreateRentalNPC()
    if not Config.NPC.enabled then return end
    
    local model = Config.NPC.model
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end
    
    rentalNPC = CreatePed(4, model, Config.NPC.coords.x, Config.NPC.coords.y, Config.NPC.coords.z - 1.0, Config.NPC.coords.w, false, true)
    FreezeEntityPosition(rentalNPC, true)
    SetEntityInvincible(rentalNPC, true)
    SetBlockingOfNonTemporaryEvents(rentalNPC, true)
    
    if Config.NPC.scenario then
        TaskStartScenarioInPlace(rentalNPC, Config.NPC.scenario, 0, true)
    end
    
    -- Etkile<PERSON><PERSON> t<PERSON> göre setup
    if Config.InteractionType == 'qb-target' then
        exports['qb-target']:AddTargetEntity(rentalNPC, {
            options = {
                {
                    type = "client",
                    event = "carrent:client:openMenu",
                    icon = "fas fa-car",
                    label = "Araç Kirala",
                }
            },
            distance = 3.0
        })
    end
end

-- Blip oluşturma
local function CreateBlip()
    if not Config.Blip.enabled then return end
    
    local blip = AddBlipForCoord(Config.NPC.coords.x, Config.NPC.coords.y, Config.NPC.coords.z)
    SetBlipSprite(blip, Config.Blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.Blip.scale)
    SetBlipColour(blip, Config.Blip.color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.Blip.label)
    EndTextCommandSetBlipName(blip)
end

-- UI Açma/Kapama Fonksiyonları
function ToggleRentalMenu(show)
    SetNuiFocus(show, show)
    SendNUIMessage({
        action = "toggleMenu",
        show = show
    })
end

function ShowCarMenu()
    local cars = {}
    for i, car in ipairs(Config.RentalCars) do
        table.insert(cars, {
            model = car.model,
            label = car.label,
            category = car.category,
            price = car.price
        })
    end
    
    SendNUIMessage({
        action = "showCarMenu",
        cars = cars
    })
end

function ShowTimeMenu(car)
    SendNUIMessage({
        action = "showTimeMenu",
        car = car
    })
end

-- Araç kiralama menüsü
function OpenRentalMenu()
    ShowCarMenu()
    ToggleRentalMenu(true)
end

-- Interact sistemi için kontrol
CreateThread(function()
    if Config.InteractionType == 'interact' then
        while true do
            local sleep = 1000
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local npcCoords = vector3(Config.NPC.coords.x, Config.NPC.coords.y, Config.NPC.coords.z)
            local distance = #(playerCoords - npcCoords)
            
            if distance < 3.0 then
                sleep = 0
                if distance < 2.0 then
                    lib.showTextUI('[E] Araç Kirala', {
                        position = "top-center",
                    })
                    
                    if IsControlJustReleased(0, 38) then -- E tuşu
                        lib.hideTextUI()
                        OpenRentalMenu()
                    end
                else
                    lib.hideTextUI()
                end
            end
            
            Wait(sleep)
        end
    end
end)

-- Kiralık araç spawn etme
RegisterNetEvent('carrent:client:spawnCar', function(carData, rentalTime)
    local model = carData.model
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end
    
    local vehicle = CreateVehicle(model, Config.SpawnPoint.x, Config.SpawnPoint.y, Config.SpawnPoint.z, Config.SpawnPoint.w, true, false)
    SetEntityAsMissionEntity(vehicle, true, true)
    SetVehicleNumberPlateText(vehicle, "RENTAL")
    
    -- Oyuncuyu araca bindir
    local playerPed = PlayerPedId()
    SetPedIntoVehicle(playerPed, vehicle, -1)
    
    -- Aracın anahtarını ver
    TriggerEvent("vehiclekeys:client:SetOwner", QBCore.Functions.GetPlate(vehicle))
    
    -- Kiralama bilgilerini sakla
    currentRental = {
        vehicle = vehicle,
        endTime = GetGameTimer() + (rentalTime * 60 * 1000),
        plate = QBCore.Functions.GetPlate(vehicle)
    }
    
    ShowNotification(string.format('%s başarıyla kiralandı! Süre: %d dakika', carData.label, rentalTime), 'success')
    
    -- Uyarı timer başlat
    SetTimeout((rentalTime - Config.RentalSettings.warningTime) * 60 * 1000, function()
        if currentRental and DoesEntityExist(currentRental.vehicle) then
            ShowNotification(string.format('Kiralama süreniz %d dakika içinde bitecek!', Config.RentalSettings.warningTime), 'warning')
        end
    end)
    
    -- Süre bitince aracı sil
    SetTimeout(rentalTime * 60 * 1000, function()
        if currentRental and DoesEntityExist(currentRental.vehicle) then
            DeleteEntity(currentRental.vehicle)
            currentRental = nil
            ShowNotification('Kiralama süreniz doldu, araç geri alındı.', 'error')
        end
    end)
end)

-- Kalan süreyi kontrol etme
RegisterCommand(Config.Commands.checkTime, function()
    if not currentRental then
        ShowNotification('Şu anda kiralık aracınız bulunmuyor.', 'error')
        return
    end
    
    if not DoesEntityExist(currentRental.vehicle) then
        currentRental = nil
        ShowNotification('Kiralık aracınız bulunamadı.', 'error')
        return
    end
    
    local remainingTime = currentRental.endTime - GetGameTimer()
    if remainingTime <= 0 then
        ShowNotification('Kiralama süreniz dolmuş.', 'error')
        return
    end
    
    local minutes = math.floor(remainingTime / 60000)
    local seconds = math.floor((remainingTime % 60000) / 1000)
    
    ShowNotification(string.format('Kalan süre: %d dakika %d saniye', minutes, seconds), 'info')
end)

-- NUI Callbacks
RegisterNUICallback('rentCar', function(data, cb)
    local car = data.car
    local time = tonumber(data.time)
    
    if time and car then
        local totalPrice = car.price * time
        TriggerServerEvent('carrent:server:rentCar', car, time, totalPrice)
    end
    cb('ok')
end)

RegisterNUICallback('closeMenu', function(_, cb)
    ToggleRentalMenu(false)
    cb('ok')
end)

RegisterNUICallback('showTimeMenu', function(data, cb)
    ShowTimeMenu(data.car)
    cb('ok')
end)

RegisterNUICallback('showCarMenu', function(_, cb)
    ShowCarMenu()
    cb('ok')
end)

-- Event handlers
RegisterNetEvent('carrent:client:openMenu', function()
    OpenRentalMenu()
end)

RegisterNetEvent('carrent:client:rentalFailed', function(reason)
    ShowNotification(reason, 'error')
end)

-- Script başlatıldığında
CreateThread(function()
    CreateRentalNPC()
    CreateBlip()
    
    -- UI kaynaklarını yükle
    RequestStreamedTextureDict("commonmenu", true)
    while not HasStreamedTextureDictLoaded("commonmenu") do
        Wait(1)
    end
end)

-- Kaynak durdurulduğunda temizlik
AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        if rentalNPC then
            DeleteEntity(rentalNPC)
        end
        if currentRental and DoesEntityExist(currentRental.vehicle) then
            DeleteEntity(currentRental.vehicle)
        end
        lib.hideTextUI()
        ToggleRentalMenu(false)
    end
end)