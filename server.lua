local QBCore = exports['qb-core']:GetCoreObject()

-- A<PERSON><PERSON>r
local activeRentals = {}

-- Araç kiralama eventi
RegisterNetEvent('carrent:server:rentCar', function(carData, rentalTime, totalPrice)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player then
        TriggerClientEvent('carrent:client:rentalFailed', src, 'Oyuncu verisi bulunamadı!')
        return
    end
    
    -- Oyuncunun zaten kiralık aracı var mı kontrol et
    if activeRentals[Player.PlayerData.citizenid] then
        TriggerClientEvent('carrent:client:rentalFailed', src, 'Zaten kiralık bir aracınız var!')
        return
    end
    
    -- Para kontrolü
    if Player.PlayerData.money.cash < totalPrice then
        if Player.PlayerData.money.bank < totalPrice then
            TriggerClientEvent('carrent:client:rentalFailed', src, 'Yet<PERSON><PERSON> paranız yok!')
            return
        else
            -- Bankadan çek
            if not Player.Functions.RemoveMoney('bank', totalPrice, 'car-rental') then
                TriggerClientEvent('carrent:client:rentalFailed', src, 'Para çekme işlemi başarısız!')
                return
            end
        end
    else
        -- Nakit olarak öde
        if not Player.Functions.RemoveMoney('cash', totalPrice, 'car-rental') then
            TriggerClientEvent('carrent:client:rentalFailed', src, 'Para çekme işlemi başarısız!')
            return
        end
    end
    
    -- Kiralama kaydını oluştur
    activeRentals[Player.PlayerData.citizenid] = {
        carModel = carData.model,
        startTime = os.time(),
        endTime = os.time() + (rentalTime * 60),
        rentalTime = rentalTime,
        price = totalPrice
    }
    
    -- Aracı spawn et
    TriggerClientEvent('carrent:client:spawnCar', src, carData, rentalTime)
    
    -- Log kaydı
    print(string.format('[CarRent] %s (%s) rented %s for %d minutes ($%d)', 
        Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname,
        Player.PlayerData.citizenid,
        carData.label,
        rentalTime,
        totalPrice
    ))
    
    -- Kiralama süresinin sonunda temizlik yap
    SetTimeout(rentalTime * 60 * 1000, function()
        if activeRentals[Player.PlayerData.citizenid] then
            activeRentals[Player.PlayerData.citizenid] = nil
        end
    end)
end)

-- Oyuncu çıktığında temizlik
RegisterNetEvent('QBCore:Server:OnPlayerUnload', function(src)
    local Player = QBCore.Functions.GetPlayer(src)
    if Player and activeRentals[Player.PlayerData.citizenid] then
        activeRentals[Player.PlayerData.citizenid] = nil
    end
end)

-- Kalan süreyi kontrol etme komutu (sunucu tarafı)
QBCore.Commands.Add(Config.Commands.checkTime, 'Kiralık araç sürenizi kontrol edin', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    local rental = activeRentals[Player.PlayerData.citizenid]
    if not rental then
        TriggerClientEvent('QBCore:Notify', source, 'Şu anda kiralık aracınız bulunmuyor.', 'error')
        return
    end
    
    local currentTime = os.time()
    local remainingTime = rental.endTime - currentTime
    
    if remainingTime <= 0 then
        activeRentals[Player.PlayerData.citizenid] = nil
        TriggerClientEvent('QBCore:Notify', source, 'Kiralama süreniz dolmuş.', 'error')
        return
    end
    
    local minutes = math.floor(remainingTime / 60)
    local seconds = remainingTime % 60
    
    TriggerClientEvent('QBCore:Notify', source, string.format('Kalan süre: %d dakika %d saniye', minutes, seconds), 'primary')
end)

-- Admin komutu - tüm kiralamaları göster
QBCore.Commands.Add('rentaladmin', 'Tüm aktif kiralamaları göster (Admin)', {}, false, function(source, args)
    local Player = QBCore.Functions.GetPlayer(source)
    if not Player then return end
    
    -- Admin kontrolü
    if not QBCore.Functions.HasPermission(source, 'admin') then
        TriggerClientEvent('QBCore:Notify', source, 'Bu komutu kullanma yetkiniz yok!', 'error')
        return
    end
    
    local rentalCount = 0
    for citizenid, rental in pairs(activeRentals) do
        rentalCount = rentalCount + 1
        local remainingTime = rental.endTime - os.time()
        local minutes = math.floor(remainingTime / 60)
        local seconds = remainingTime % 60
        
        print(string.format('[CarRent Admin] CitizenID: %s | Model: %s | Remaining: %dm %ds', 
            citizenid, rental.carModel, minutes, seconds))
    end
    
    TriggerClientEvent('QBCore:Notify', source, string.format('Toplam %d aktif kiralama bulundu. Konsolu kontrol edin.', rentalCount), 'success')
end, 'admin')

-- Kaynak durdurulduğunda temizlik
AddEventHandler('onResourceStop', function(resource)
    if resource == GetCurrentResourceName() then
        activeRentals = {}
        print('[CarRent] Resource stopped, all rentals cleared.')
    end
end)

-- Kaynak başlatıldığında
AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        print('[CarRent] Resource started successfully!')
        print('[CarRent] Available cars: ' .. #Config.RentalCars)
        print('[CarRent] Interaction type: ' .. Config.InteractionType)
        print('[CarRent] Check time command: /' .. Config.Commands.checkTime)
    end
end)