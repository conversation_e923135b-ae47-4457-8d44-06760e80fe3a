Config = {}

-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> türü: 'qb-target' veya 'interact'
Config.InteractionType = 'qb-target' -- 'qb-target' veya 'interact'

-- NPC Ayarları
Config.NPC = {
    enabled = true,
    model = 'a_m_m_business_01',
    coords = vector4(-1037.58, -2730.67, 20.17, 240.0), -- LSIA yakını
    scenario = 'WORLD_HUMAN_CLIPBOARD'
}

-- <PERSON>ç Spawn Noktası
Config.SpawnPoint = vector4(-1041.58, -2728.67, 20.17, 60.0)

-- Kiralama Ayarları
Config.RentalSettings = {
    defaultTime = 30, -- Dakika
    maxTime = 120, -- <PERSON><PERSON><PERSON><PERSON> kiralama süresi (dakika)
    minTime = 1, -- Minimum kiralama süresi (dakika)
    deleteOnExpire = true, -- S<PERSON>re bitince aracı sil
    warningTime = 5 -- <PERSON><PERSON> dakika kala uyarı verilecek
}

-- <PERSON><PERSON><PERSON><PERSON>ç<PERSON> ve Fiyatları
Config.RentalCars = {
    {
        model = 'sultan',
        label = 'Sultan',
        price = 100, -- <PERSON><PERSON>ka başına fiyat
        category = 'Sedan'
    },
    {
        model = 'elegy2',
        label = 'Elegy RH8',
        price = 150,
        category = 'Sports'
    },
    {
        model = 'adder',
        label = 'Adder',
        price = 300,
        category = 'Super'
    },
    {
        model = 'sandking',
        label = 'Sandking XL',
        price = 120,
        category = 'SUV'
    },
    {
        model = 'bison',
        label = 'Bison',
        price = 80,
        category = 'Van'
    }
}

-- Blip Ayarları
Config.Blip = {
    enabled = true,
    sprite = 225,
    color = 5,
    scale = 0.8,
    label = 'Araç Kiralama'
}

-- Komut Ayarları
Config.Commands = {
    checkTime = 'rentaltime' -- Kalan süreyi kontrol etme komutu
}